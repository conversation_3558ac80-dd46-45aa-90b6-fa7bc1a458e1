import { json } from '@sveltejs/kit';
import { readdir, readFile } from 'fs/promises';
import { join } from 'path';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ url }) => {
  const filename = url.searchParams.get('file');
  
  try {
    const docsPath = join(process.cwd(), 'docs');
    
    if (filename) {
      // Serve specific file content
      const filePath = join(docsPath, filename);
      const content = await readFile(filePath, 'utf-8');
      return new Response(content, {
        headers: {
          'Content-Type': 'text/plain'
        }
      });
    } else {
      // List all markdown files
      const files = await readdir(docsPath);
      const markdownFiles = files
        .filter((file: string) => file.endsWith('.md'))
        .map((file: string) => ({
          id: file.replace('.md', '').toLowerCase().replace(/[^a-z0-9]/g, '-'),
          label: file.replace('.md', '').replace(/-/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase()),
          filename: file
        }));
      
      return json(markdownFiles);
    }
  } catch (error) {
    console.error('Error reading docs:', error);
    return json({ error: 'Failed to read documentation files' }, { status: 500 });
  }
}; 