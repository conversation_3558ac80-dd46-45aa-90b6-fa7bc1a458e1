<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import { addToast } from '$lib/stores/toastStore';
  import Button from '$lib/components/Button.svelte';
  import PageHeader from '$lib/components/PageHeader.svelte';
  import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
  import Tabs from '$lib/components/Tabs.svelte';
  import { user } from '$lib/stores/auth';
  import { api, ApiError } from '$lib/utils/api';
  import type { Contact, ContactEmail, ContactPhone, ContactAddress } from '$lib/api/contacts';
  import { addContactNote, updateContactNote, deleteContactNote, type ContactNote } from '$lib/api/contacts';
  import { getInvoicesByCustomer, getStatusDisplay, type ApiInvoice } from '$lib/api/invoices';
  import { getJobsByCustomer, type Job } from '$lib/api/jobs';
  import { getCalendarEvents, type CalendarEvent } from '$lib/api/calendar';
  import { formatCurrency } from '$lib/config/currency';
  import Modal from '$lib/components/Modal.svelte';
  import Grid from '$lib/components/Grid.svelte';

  // Enhanced Customer interface based on Contact structure
  interface Customer {
    id: string;
    name: string;
    email: string;
    phone: string;
    // Enhanced fields from contacts
    fullName?: string;
    companyName?: string;
    emails?: ContactEmail[];
    phones?: ContactPhone[];
    addresses?: ContactAddress[];
    status?: 'Lead' | 'Customer' | 'Archived';
    notes?: any[];
    checklists?: any[];
    communicationTimeline?: any[];
    createdAt?: string;
    updatedAt?: string;
  }

  interface UpdateCustomerDto {
    name: string;
    email: string;
    phone: string;
  }

  let customer: Customer | null = null;
  let loading = true;
  let saving = false;
  let deleting = false;
  let error: string | null = null;
  let customerId: string;

  // Form fields
  let name = '';
  let email = '';
  let phone = '';

  // Track if form has been modified
  let isModified = false;

  // Tab management
  let activeTab = 'details';

  // New data for additional tabs
  let customerInvoices: ApiInvoice[] = [];
  let customerJobs: Job[] = [];
  let customerEvents: CalendarEvent[] = [];
  let loadingInvoices = false;
  let loadingJobs = false;
  let loadingEvents = false;

  // Note management
  let showAddNoteModal = false;
  let showEditNoteModal = false;
  let noteContent = '';
  let editingNote: ContactNote | null = null;
  let savingNote = false;

  // Tab configuration - updated with new tabs
  const tabs = [
    { id: 'details', label: 'Details' },
    { id: 'invoices', label: 'Invoices' },
    { id: 'schedule', label: 'Schedule' },
    { id: 'jobs', label: 'Jobs' },
    { id: 'notes', label: 'Notes' },
    { id: 'checklists', label: 'Checklists' },
    { id: 'timeline', label: 'Timeline' }
  ];

  // Redirect to login if not logged in
  onMount(() => {
    const unsubscribe = user.subscribe(value => {
      if (!value) {
        goto('/login');
      }
    });

    // Get customer ID from URL params
    customerId = $page.params.id;
    if (customerId) {
      fetchCustomer();
    } else {
      error = 'No customer ID provided';
      loading = false;
    }

    return unsubscribe;
  });

  async function fetchCustomer() {
    loading = true;
    error = null;
    try {
      const response = await api.get<Customer>(`/Customers/${customerId}`);
      customer = response;
      // Initialize form fields
      name = customer.name;
      email = customer.email;
      phone = customer.phone;
      isModified = false;
      
      // Load additional customer data
      await loadCustomerData();
    } catch (err) {
      if (err instanceof ApiError) {
        error = `Failed to fetch customer: ${err.message}`;
        console.error('API Error:', err.status, err.message);
      } else {
        error = 'An unexpected error occurred while fetching customer details.';
        console.error('Fetch customer error:', err);
      }
    } finally {
      loading = false;
    }
  }

  async function loadCustomerData() {
    // Load all customer-related data in parallel
    await Promise.all([
      loadCustomerInvoices(),
      loadCustomerJobs(),
      loadCustomerEvents()
    ]);
  }

  async function loadCustomerInvoices() {
    if (!customerId) return;
    
    loadingInvoices = true;
    try {
      customerInvoices = await getInvoicesByCustomer(customerId);
    } catch (err) {
      console.error('Error loading customer invoices:', err);
      addToast({ message: 'Failed to load customer invoices', type: 'error' });
    } finally {
      loadingInvoices = false;
    }
  }

  async function loadCustomerJobs() {
    if (!customerId) return;
    
    loadingJobs = true;
    try {
      customerJobs = await getJobsByCustomer(customerId);
    } catch (err) {
      console.error('Error loading customer jobs:', err);
      addToast({ message: 'Failed to load customer jobs', type: 'error' });
    } finally {
      loadingJobs = false;
    }
  }

  async function loadCustomerEvents() {
    if (!customerId) return;
    
    loadingEvents = true;
    try {
      // Get events for a wide date range to capture all customer events
      const startDate = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(); // 1 year ago
      const endDate = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(); // 1 year from now
      
      const allEvents = await getCalendarEvents(startDate, endDate);
      // Filter events for this customer
      customerEvents = allEvents.filter(event => event.customerId === customerId);
    } catch (err) {
      console.error('Error loading customer events:', err);
      addToast({ message: 'Failed to load customer events', type: 'error' });
    } finally {
      loadingEvents = false;
    }
  }

  async function handleSave() {
    if (!customer || !isModified) return;

    saving = true;
    error = null;

    try {
      const updateDto: UpdateCustomerDto = {
        name: name.trim(),
        email: email.trim(),
        phone: phone.trim()
      };

      await api.put(`/Customers/${customerId}`, updateDto);

      // Update local customer object
      customer = {
        ...customer,
        name: updateDto.name,
        email: updateDto.email,
        phone: updateDto.phone
      };

      isModified = false;
      addToast({ message: 'Customer updated successfully', type: 'success' });
    } catch (err) {
      if (err instanceof ApiError) {
        error = `Failed to update customer: ${err.message}`;
        console.error('API Error:', err.status, err.message);
        addToast({ message: `Failed to update customer: ${err.message}`, type: 'error' });
      } else {
        error = 'An unexpected error occurred while updating customer.';
        console.error('Update customer error:', err);
        addToast({ message: 'An unexpected error occurred while updating customer.', type: 'error' });
      }
    } finally {
      saving = false;
    }
  }

  async function handleDelete() {
    if (!customer) return;

    if (!confirm(`Are you sure you want to delete customer "${customer.name}"? This action cannot be undone.`)) {
      return;
    }

    deleting = true;
    error = null;

    try {
      await api.delete(`/Customers/${customerId}`);
      addToast({ message: 'Customer deleted successfully', type: 'success' });
      goto('/customers');
    } catch (err) {
      if (err instanceof ApiError) {
        error = `Failed to delete customer: ${err.message}`;
        console.error('API Error:', err.status, err.message);
        addToast({ message: `Failed to delete customer: ${err.message}`, type: 'error' });
      } else {
        error = 'An unexpected error occurred while deleting customer.';
        console.error('Delete customer error:', err);
        addToast({ message: 'An unexpected error occurred while deleting customer.', type: 'error' });
      }
    } finally {
      deleting = false;
    }
  }

  function handleBack() {
    if (isModified) {
      if (!confirm('You have unsaved changes. Are you sure you want to leave?')) {
        return;
      }
    }
    goto('/customers');
  }

  function handleInputChange() {
    if (customer) {
      isModified = name !== customer.name || email !== customer.email || phone !== customer.phone;
    }
  }

  // Note management functions
  async function addNote() {
    if (!noteContent.trim() || !customerId) return;

    savingNote = true;
    try {
      const newNote = await addContactNote(customerId, noteContent.trim());
      
      // Update customer notes
      if (customer) {
        customer.notes = customer.notes || [];
        customer.notes.push(newNote);
        customer = { ...customer }; // Trigger reactivity
      }

      addToast({ message: 'Note added successfully', type: 'success' });
      closeAddNoteModal();
    } catch (err) {
      console.error('Error adding note:', err);
      addToast({ message: 'Failed to add note', type: 'error' });
    } finally {
      savingNote = false;
    }
  }

  function editNote(note: ContactNote) {
    editingNote = note;
    noteContent = note.content;
    showEditNoteModal = true;
  }

  async function updateNote() {
    if (!noteContent.trim() || !editingNote || !customerId) return;

    savingNote = true;
    try {
      const updatedNote = await updateContactNote(customerId, editingNote.id, noteContent.trim());
      
      // Update customer notes
      if (customer && customer.notes) {
        const index = customer.notes.findIndex(n => n.id === editingNote!.id);
        if (index !== -1) {
          customer.notes[index] = updatedNote;
          customer = { ...customer }; // Trigger reactivity
        }
      }

      addToast({ message: 'Note updated successfully', type: 'success' });
      closeEditNoteModal();
    } catch (err) {
      console.error('Error updating note:', err);
      addToast({ message: 'Failed to update note', type: 'error' });
    } finally {
      savingNote = false;
    }
  }

  async function deleteNote(noteId: string) {
    if (!confirm('Are you sure you want to delete this note?')) return;

    try {
      await deleteContactNote(customerId, noteId);
      
      // Update customer notes
      if (customer && customer.notes) {
        customer.notes = customer.notes.filter(n => n.id !== noteId);
        customer = { ...customer }; // Trigger reactivity
      }

      addToast({ message: 'Note deleted successfully', type: 'success' });
    } catch (err) {
      console.error('Error deleting note:', err);
      addToast({ message: 'Failed to delete note', type: 'error' });
    }
  }

  function closeAddNoteModal() {
    showAddNoteModal = false;
    noteContent = '';
  }

  function closeEditNoteModal() {
    showEditNoteModal = false;
    noteContent = '';
    editingNote = null;
  }

  // Reactive statement to track changes
  $: if (customer) {
    isModified = name !== customer.name || email !== customer.email || phone !== customer.phone;
  }
</script>

<svelte:head>
  <title>{customer ? `${customer.name} - Edit Customer` : 'Edit Customer'}</title>
</svelte:head>

<div class="container">
  <PageHeader title={customer ? `Edit ${customer.name}` : 'Edit Customer'}>
    <svelte:fragment slot="actions">
      <Button on:click={handleBack} variant="secondary" type="button">Back to Customers</Button>
      {#if customer}
        <Button
          on:click={handleDelete}
          variant="danger"
          type="button"
          disabled={deleting}
        >
          {deleting ? 'Deleting...' : 'Delete Customer'}
        </Button>
        <Button
          on:click={handleSave}
          variant="primary"
          type="button"
          disabled={!isModified || saving}
        >
          {saving ? 'Saving...' : 'Save Changes'}
        </Button>
      {/if}
    </svelte:fragment>
  </PageHeader>

  <main>
    {#if loading}
      <LoadingSpinner message="Loading customer details..." />
    {:else if error}
      <div class="error-container">
        <p class="error-message">{error}</p>
        <Button on:click={fetchCustomer} type="button">Retry</Button>
      </div>
    {:else if customer}
      <div class="customer-details">
        <!-- Tabs Navigation -->
        <Tabs {tabs} bind:activeTab />

        <!-- Tab Content -->
        <div class="tab-content">
          {#if activeTab === 'details'}
            <div class="details-section">
              <h2>Customer Information</h2>

              <form class="details-form" on:submit|preventDefault={handleSave}>
                <div class="form-group">
                  <label for="name">Name</label>
                  <input
                    id="name"
                    type="text"
                    bind:value={name}
                    on:input={handleInputChange}
                    required
                    disabled={saving || deleting}
                    class="form-input"
                  />
                </div>

                <div class="form-group">
                  <label for="email">Email</label>
                  <input
                    id="email"
                    type="email"
                    bind:value={email}
                    on:input={handleInputChange}
                    required
                    disabled={saving || deleting}
                    class="form-input"
                  />
                </div>

                <div class="form-group">
                  <label for="phone">Phone</label>
                  <input
                    id="phone"
                    type="tel"
                    bind:value={phone}
                    on:input={handleInputChange}
                    required
                    disabled={saving || deleting}
                    class="form-input"
                  />
                </div>
              </form>

              <!-- Enhanced Contact Information Display -->
              {#if customer.emails && customer.emails.length > 0}
                <div class="contact-section">
                  <h3>All Email Addresses</h3>
                  {#each customer.emails as email}
                    <div class="contact-item" class:primary={email.isPrimary}>
                      <div class="item-value">{email.email}</div>
                      <span class="item-type">{email.type}</span>
                      {#if email.isPrimary}<span class="primary-badge">Primary</span>{/if}
                    </div>
                  {/each}
                </div>
              {/if}

              {#if customer.phones && customer.phones.length > 0}
                <div class="contact-section">
                  <h3>All Phone Numbers</h3>
                  {#each customer.phones as phone}
                    <div class="contact-item" class:primary={phone.isPrimary}>
                      <div class="item-value">{phone.phone}</div>
                      <span class="item-type">{phone.type}</span>
                      {#if phone.isPrimary}<span class="primary-badge">Primary</span>{/if}
                    </div>
                  {/each}
                </div>
              {/if}

              {#if customer.addresses && customer.addresses.length > 0}
                <div class="contact-section">
                  <h3>All Addresses</h3>
                  {#each customer.addresses as address}
                    <div class="contact-item" class:primary={address.isPrimary}>
                      <div class="item-value">
                        {address.street}<br>
                        {address.city}, {address.state} {address.zipCode}
                      </div>
                      <span class="item-type">{address.type}</span>
                      {#if address.isPrimary}<span class="primary-badge">Primary</span>{/if}
                    </div>
                  {/each}
                </div>
              {/if}
            </div>
          {:else if activeTab === 'invoices'}
            <div class="invoices-section">
              <div class="section-header">
                <h2>Customer Invoices</h2>
                <Button on:click={() => goto(`/invoices/new?customerId=${customerId}`)} variant="primary" size="small">
                  Create New Invoice
                </Button>
              </div>
              
              {#if loadingInvoices}
                <LoadingSpinner message="Loading invoices..." />
              {:else if customerInvoices.length === 0}
                <div class="empty-state">
                  <p>No invoices found for this customer.</p>
                  <Button on:click={() => goto(`/invoices/new?customerId=${customerId}`)} variant="primary">
                    Create First Invoice
                  </Button>
                </div>
              {:else}
                <div class="data-table">
                  <div class="table-header">
                    <div class="header-cell">Invoice #</div>
                    <div class="header-cell">Date</div>
                    <div class="header-cell">Status</div>
                    <div class="header-cell">Amount</div>
                    <div class="header-cell">Balance</div>
                    <div class="header-cell">Actions</div>
                  </div>
                  {#each customerInvoices as invoice}
                    <div class="table-row">
                      <div class="table-cell">
                        <strong>{invoice.invoiceNumber}</strong>
                      </div>
                      <div class="table-cell">
                        {new Date(invoice.issueDate).toLocaleDateString()}
                      </div>
                      <div class="table-cell">
                        <span class="status-badge" style="background-color: {getStatusDisplay(invoice.status).color}20; color: {getStatusDisplay(invoice.status).color};">
                          {getStatusDisplay(invoice.status).name}
                        </span>
                      </div>
                      <div class="table-cell">
                        {formatCurrency(invoice.invoiceLines.reduce((sum, line) => sum + line.total, 0))}
                      </div>
                      <div class="table-cell">
                        {formatCurrency(invoice.invoiceLines.reduce((sum, line) => sum + line.total, 0))}
                      </div>
                      <div class="table-cell">
                        <div class="action-buttons">
                          <Button on:click={() => goto(`/invoices/${invoice.id}`)} variant="tertiary" size="small">
                            View
                          </Button>
                          <Button on:click={() => goto(`/invoices/${invoice.id}/edit`)} variant="secondary" size="small">
                            Edit
                          </Button>
                        </div>
                      </div>
                    </div>
                  {/each}
                </div>
                
                <div class="summary-stats">
                  <div class="stat-card">
                    <div class="stat-label">Total Invoices</div>
                    <div class="stat-value">{customerInvoices.length}</div>
                  </div>
                  <div class="stat-card">
                    <div class="stat-label">Total Amount</div>
                    <div class="stat-value">{formatCurrency(customerInvoices.reduce((sum, inv) => sum + inv.invoiceLines.reduce((lineSum, line) => lineSum + line.total, 0), 0))}</div>
                  </div>
                  <div class="stat-card">
                    <div class="stat-label">Outstanding Balance</div>
                    <div class="stat-value">{formatCurrency(customerInvoices.reduce((sum, inv) => sum + inv.invoiceLines.reduce((lineSum, line) => lineSum + line.total, 0), 0))}</div>
                  </div>
                </div>
              {/if}
            </div>
          {:else if activeTab === 'schedule'}
            <div class="schedule-section">
              <div class="section-header">
                <h2>Schedule</h2>
                <Button on:click={() => goto(`/calendar?customerId=${customerId}`)} variant="primary" size="small">
                  Schedule New Visit
                </Button>
              </div>
              
              {#if loadingEvents}
                <LoadingSpinner message="Loading schedule..." />
              {:else}
                {@const allEvents = customerEvents.sort((a, b) => new Date(a.startDateTime).getTime() - new Date(b.startDateTime).getTime())}
                {#if allEvents.length === 0}
                  <div class="empty-state">
                    <p>No events found for this customer.</p>
                    <Button on:click={() => goto(`/calendar?customerId=${customerId}`)} variant="primary">
                      Schedule First Visit
                    </Button>
                  </div>
                {:else}
                  <Grid
                    headers={[
                      { text: 'Date', key: 'startDateTime', sortable: true },
                      { text: 'Service', key: 'title', sortable: true },
                      { text: 'Status', key: 'status', sortable: true },
                      { text: 'Staff', key: 'assignedStaff', sortable: false },
                      { text: 'Duration', key: 'duration', sortable: false },
                      { text: 'Priority', key: 'priority', sortable: true },
                      { text: 'Actions', key: 'actions', sortable: false }
                    ]}
                    dataRows={allEvents}
                    emptyMessage="No events found for this customer."
                    totalItems={allEvents.length}
                    itemsPerPage={25}
                    currentPage={1}
                  >
                    <svelte:fragment slot="cell" let:row let:headerKey let:value>
                      {#if headerKey === 'startDateTime'}
                        <div class="date-info">
                          <strong>{new Date(row.startDateTime).toLocaleDateString()}</strong>
                          <small>{new Date(row.startDateTime).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</small>
                        </div>
                      {:else if headerKey === 'title'}
                        <div class="service-info">
                          <strong>{row.title}</strong>
                          {#if row.description}
                            <small>{row.description}</small>
                          {/if}
                        </div>
                      {:else if headerKey === 'status'}
                        <span class="status-badge {row.status.toLowerCase()}">
                          {row.status}
                        </span>
                      {:else if headerKey === 'assignedStaff'}
                        <div class="staff-list">
                          {#each row.assignedStaff as staff}
                            <span class="staff-badge">{staff.staffName}</span>
                          {/each}
                        </div>
                      {:else if headerKey === 'duration'}
                        {#if new Date(row.startDateTime) < new Date() && row.actualDuration}
                          {Math.floor(row.actualDuration / 60)}h {row.actualDuration % 60}m
                        {:else if row.estimatedDuration}
                          ~{Math.floor(row.estimatedDuration / 60)}h {row.estimatedDuration % 60}m
                        {:else}
                          -
                        {/if}
                      {:else if headerKey === 'priority'}
                        <span class="priority-badge {row.priority.toLowerCase()}">
                          {row.priority}
                        </span>
                      {:else if headerKey === 'actions'}
                        <div class="action-buttons">
                          <Button on:click={() => goto(`/calendar?eventId=${row.id}`)} variant="tertiary" size="small">
                            View
                          </Button>
                          {#if new Date(row.startDateTime) > new Date()}
                            <Button on:click={() => goto(`/calendar?eventId=${row.id}&action=edit`)} variant="secondary" size="small">
                              Reschedule
                            </Button>
                          {:else if row.jobId}
                            <Button on:click={() => goto(`/invoices/new?jobId=${row.jobId}&customerId=${customerId}`)} variant="secondary" size="small">
                              Invoice
                            </Button>
                          {/if}
                        </div>
                      {:else}
                        {value}
                      {/if}
                    </svelte:fragment>
                  </Grid>
                {/if}
              {/if}
            </div>
          {:else if activeTab === 'jobs'}
            <div class="jobs-section">
              <div class="section-header">
                <h2>Customer Jobs</h2>
                <Button on:click={() => goto(`/jobs/new?customerId=${customerId}`)} variant="primary" size="small">
                  Create New Job
                </Button>
              </div>
              
              {#if loadingJobs}
                <LoadingSpinner message="Loading jobs..." />
              {:else if customerJobs.length === 0}
                <div class="empty-state">
                  <p>No jobs found for this customer.</p>
                  <Button on:click={() => goto(`/jobs/new?customerId=${customerId}`)} variant="primary">
                    Create First Job
                  </Button>
                </div>
              {:else}
                <Grid
                  headers={[
                    { text: 'Job ID', key: 'id', sortable: true },
                    { text: 'Title', key: 'title', sortable: true },
                    { text: 'Status', key: 'status', sortable: true },
                    { text: 'Priority', key: 'priority', sortable: true },
                    { text: 'Created', key: 'createdAt', sortable: true },
                    { text: 'Actions', key: 'actions', sortable: false }
                  ]}
                  dataRows={customerJobs}
                  emptyMessage="No jobs found for this customer."
                  totalItems={customerJobs.length}
                  itemsPerPage={25}
                  currentPage={1}
                >
                  <svelte:fragment slot="cell" let:row let:headerKey let:value>
                    {#if headerKey === 'id'}
                      <strong>{row.id}</strong>
                    {:else if headerKey === 'title'}
                      {row.title}
                    {:else if headerKey === 'status'}
                      <span class="status-badge" style="background-color: {row.status.color}20; color: {row.status.color};">
                        {row.status.name}
                      </span>
                    {:else if headerKey === 'priority'}
                      <span class="priority-badge {row.priority.toLowerCase()}">
                        {row.priority}
                      </span>
                    {:else if headerKey === 'createdAt'}
                      {new Date(row.createdAt).toLocaleDateString()}
                    {:else if headerKey === 'actions'}
                      <div class="action-buttons">
                        <Button on:click={() => goto(`/jobs/${row.id}`)} variant="tertiary" size="small">
                          View
                        </Button>
                        <Button on:click={() => goto(`/jobs/${row.id}/edit`)} variant="secondary" size="small">
                          Edit
                        </Button>
                      </div>
                    {:else}
                      {value}
                    {/if}
                  </svelte:fragment>
                </Grid>
              {/if}
            </div>
          {:else if activeTab === 'notes'}
            <div class="notes-section">
              <div class="section-header">
                <h2>Customer Notes</h2>
                <Button on:click={() => showAddNoteModal = true} variant="primary" size="small">
                  Add Note
                </Button>
              </div>
              
              {#if customer.notes && customer.notes.length > 0}
                <div class="notes-list">
                  {#each customer.notes.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()) as note}
                    <div class="note-item">
                      <div class="note-header">
                        <div class="note-date">
                          {new Date(note.createdAt).toLocaleDateString()} at {new Date(note.createdAt).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                        </div>
                        <div class="note-actions">
                          <Button on:click={() => editNote(note)} variant="tertiary" size="small">
                            Edit
                          </Button>
                          <Button on:click={() => deleteNote(note.id)} variant="tertiary" size="small">
                            Delete
                          </Button>
                        </div>
                      </div>
                      <div class="note-content">
                        {note.content}
                      </div>
                    </div>
                  {/each}
                </div>
              {:else}
                <div class="empty-state">
                  <p>No notes found for this customer.</p>
                  <Button on:click={() => showAddNoteModal = true} variant="primary">
                    Add First Note
                  </Button>
                </div>
              {/if}
            </div>
          {:else if activeTab === 'checklists'}
            <div class="checklists-section">
              <h2>Customer Checklists</h2>
              <p>Checklists functionality will be implemented here.</p>
            </div>
          {:else if activeTab === 'timeline'}
            <div class="timeline-section">
              <h2>Communication Timeline</h2>
              <p>Timeline functionality will be implemented here.</p>
            </div>
          {/if}
        </div>
      </div>
    {:else}
      <p>Customer not found.</p>
    {/if}
  </main>
</div>

<!-- Note Modals -->
{#if showAddNoteModal}
  <Modal title="Add Note" on:close={closeAddNoteModal}>
    <form on:submit|preventDefault={addNote} class="note-form">
      <div class="form-group">
        <label for="noteContent">Note Content</label>
        <textarea
          id="noteContent"
          bind:value={noteContent}
          placeholder="Enter your note here..."
          rows="4"
          required
          disabled={savingNote}
        ></textarea>
      </div>
      
      <div class="modal-actions">
        <Button on:click={closeAddNoteModal} variant="secondary" disabled={savingNote}>
          Cancel
        </Button>
        <Button type="submit" variant="primary" disabled={savingNote || !noteContent.trim()}>
          {savingNote ? 'Adding...' : 'Add Note'}
        </Button>
      </div>
    </form>
  </Modal>
{/if}

{#if showEditNoteModal}
  <Modal title="Edit Note" on:close={closeEditNoteModal}>
    <form on:submit|preventDefault={updateNote} class="note-form">
      <div class="form-group">
        <label for="editNoteContent">Note Content</label>
        <textarea
          id="editNoteContent"
          bind:value={noteContent}
          placeholder="Enter your note here..."
          rows="4"
          required
          disabled={savingNote}
        ></textarea>
      </div>
      
      <div class="modal-actions">
        <Button on:click={closeEditNoteModal} variant="secondary" disabled={savingNote}>
          Cancel
        </Button>
        <Button type="submit" variant="primary" disabled={savingNote || !noteContent.trim()}>
          {savingNote ? 'Updating...' : 'Update Note'}
        </Button>
      </div>
    </form>
  </Modal>
{/if}

<style lang="less">
  .customer-details-page {
    padding: 2rem;
  }

  .error-container {
    text-align: center;
    padding: 2rem;

    .error-message {
      color: var(--error);
      margin-bottom: 1rem;
      font-size: 1rem;
    }
  }

  .customer-details {
    max-width: 1000px;
    margin: 0 auto;
  }

  .tab-content {
    margin-top: 1rem;
  }

  .details-section,
  .invoices-section,
  .schedule-section,
  .jobs-section,
  .notes-section,
  .checklists-section,
  .timeline-section {
    background: white;
    border: 1px solid var(--border);
    border-radius: var(--br);
    padding: 2rem;
    margin-bottom: 2rem;

    h2 {
      margin: 0 0 1.5rem 0;
      font-size: 1.25rem;
      color: var(--text);
      border-bottom: 1px solid var(--border);
      padding-bottom: 0.75rem;
    }
  }

  .contact-section {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border);

    h3 {
      margin: 0 0 1rem 0;
      font-size: 1rem;
      color: var(--text);
      font-weight: 600;
    }
  }

  .contact-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background: var(--bg);
    border: 1px solid var(--border);
    border-radius: var(--br);
    transition: all 0.2s;

    &.primary {
      border-color: var(--primary);
      background: var(--primary-fade);
    }

    .item-value {
      flex: 1;
      font-weight: 500;
      color: var(--text);
    }

    .item-type {
      font-size: 0.8rem;
      color: var(--grey);
      margin-left: 1rem;
    }

    .primary-badge {
      background: var(--primary);
      color: white;
      padding: 0.25rem 0.5rem;
      border-radius: var(--br);
      font-size: 0.7rem;
      font-weight: 500;
      margin-left: 0.5rem;
    }
  }

  .details-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    label {
      font-weight: 500;
      font-size: 0.9rem;
      color: var(--grey);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .form-input {
      font-size: 1rem;
      color: var(--text);
      padding: 0.75rem;
      background: var(--bg);
      border: 1px solid var(--border);
      border-radius: var(--br);
      transition: border-color 0.2s ease, box-shadow 0.2s ease;

      &:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1);
      }

      &:disabled {
        background: var(--disabled-bg, #f5f5f5);
        color: var(--disabled-text, #999);
        cursor: not-allowed;
      }

      &:invalid {
        border-color: var(--error);
      }
    }
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;

    h2 {
      margin: 0;
      border: none;
      padding: 0;
    }
  }

  .empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--grey);

    p {
      margin-bottom: 1rem;
      font-size: 1rem;
    }
  }

  .data-table {
    border: 1px solid var(--border);
    border-radius: var(--br);
    overflow: hidden;
    margin-bottom: 1.5rem;

    .table-header {
      display: grid;
      grid-template-columns: 1fr 2fr 1.5fr 1fr 1fr 1.5fr;
      background: var(--bg);
      border-bottom: 1px solid var(--border);

      .header-cell {
        padding: 1rem;
        font-weight: 600;
        font-size: 0.9rem;
        color: var(--grey);
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }

    .table-row {
      display: grid;
      grid-template-columns: 1fr 2fr 1.5fr 1fr 1fr 1.5fr;
      border-bottom: 1px solid var(--border);
      transition: background-color 0.2s;

      &:hover {
        background: var(--bg);
      }

      &:last-child {
        border-bottom: none;
      }

      .table-cell {
        padding: 1rem;
        display: flex;
        align-items: center;
        font-size: 0.9rem;
      }
    }
  }

  .date-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;

    strong {
      color: var(--text);
    }

    small {
      color: var(--grey);
      font-size: 0.8rem;
    }
  }

  .service-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;

    strong {
      color: var(--text);
    }

    small {
      color: var(--grey);
      font-size: 0.8rem;
    }
  }

  .staff-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .staff-badge {
    background: var(--primary-fade);
    color: var(--primary);
    padding: 0.25rem 0.5rem;
    border-radius: var(--br);
    font-size: 0.8rem;
    font-weight: 500;
  }

  .status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: var(--br);
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;

    &.completed {
      background: #10B98120;
      color: #10B981;
    }
  }

  .priority-badge {
    padding: 0.25rem 0.75rem;
    border-radius: var(--br);
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;

    &.low {
      background: #6B728020;
      color: #6B7280;
    }

    &.medium {
      background: #F59E0B20;
      color: #F59E0B;
    }

    &.high {
      background: #EF444420;
      color: #EF4444;
    }

    &.urgent {
      background: #DC262620;
      color: #DC2626;
    }
  }

  .action-buttons {
    display: flex;
    gap: 0.5rem;
  }

  .summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
  }

  .stat-card {
    background: var(--bg);
    border: 1px solid var(--border);
    border-radius: var(--br);
    padding: 1.5rem;
    text-align: center;

    .stat-label {
      font-size: 0.9rem;
      color: var(--grey);
      margin-bottom: 0.5rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .stat-value {
      font-size: 1.5rem;
      font-weight: 600;
      color: var(--text);
    }
  }

  // Notes section styles
  .notes-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .note-item {
    background: var(--bg);
    border: 1px solid var(--border);
    border-radius: var(--br);
    padding: 1.5rem;
    transition: box-shadow 0.2s;

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .note-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--border);
  }

  .note-date {
    font-size: 0.9rem;
    color: var(--grey);
    font-weight: 500;
  }

  .note-actions {
    display: flex;
    gap: 0.5rem;
  }

  .note-content {
    color: var(--text);
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
  }

  .note-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;

    .form-group {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;

      label {
        font-weight: 500;
        color: var(--text);
      }

      textarea {
        padding: 0.75rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        font-family: inherit;
        font-size: 0.9rem;
        resize: vertical;
        min-height: 100px;

        &:focus {
          outline: none;
          border-color: var(--primary);
          box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1);
        }

        &:disabled {
          background: var(--disabled-bg, #f5f5f5);
          color: var(--disabled-text, #999);
          cursor: not-allowed;
        }
      }
    }
  }

  .modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
  }

  @media (max-width: 768px) {
    .customer-details-page {
      padding: 1rem;
    }
  }
</style>