<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import PageHeader from '$lib/components/PageHeader.svelte';
  import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
  import { addToast } from '$lib/stores/toastStore';
  import { getInvoiceById, getStatusDisplay, type ApiInvoice } from '$lib/api/invoices';
  import { formatCurrency } from '$lib/config/currency';
  import { generateInvoicePDF } from '$lib/utils/pdfGenerator';
  import Button from '$lib/components/Button.svelte';
  import { sendInvoiceEmail, getEmailHistoryForInvoice, type EmailHistoryItem, type EmailAttachment } from '$lib/api/emailService';
  import { customers } from '$lib/stores/customerStore';

  let invoiceId: string | null = null;
  let invoiceData: ApiInvoice | null = null;
  let isLoading = true;
  let error: string | null = null;
  let isSendingEmail = false;
  let emailHistory: EmailHistoryItem[] = [];
  let showEmailModal = false;
  let emailAddress = '';
  let emailSubject = '';
  let emailMessage = '';

  onMount(async () => {
    invoiceId = $page.params.invoiceId;
    if (invoiceId) {
      await fetchInvoiceDetails(invoiceId);
    } else {
      error = 'Invoice ID not provided.';
      isLoading = false;
    }
  });

  async function fetchInvoiceDetails(id: string) {
    isLoading = true;
    error = null;
    try {
      invoiceData = await getInvoiceById(id);
      if (!invoiceData) {
        throw new Error('Invoice not found');
      }
      
      // Load email history for this invoice
      emailHistory = await getEmailHistoryForInvoice(id);
      
      // Pre-populate email address if customer has one
      // Note: ApiInvoice doesn't have customerEmail or customerId, so we'll skip this for now
      
      // Set default email subject
      const invoiceTotal = invoiceData.invoiceLines.reduce((sum, line) => sum + line.total, 0);
      emailSubject = `Invoice ${invoiceData.invoiceNumber || 'Draft'} - ${formatCurrency(invoiceTotal)}`;
      
    } catch (err) {
      console.error('Error fetching invoice details:', err);
      error = err instanceof Error ? err.message : 'An unknown error occurred';
      addToast({
        type: 'error',
        message: `Failed to load invoice: ${error}`
      });
    } finally {
      isLoading = false;
    }
  }

  async function handleExportPDF() {
    if (!invoiceData) return;
    
    try {
      // TODO: Update generateInvoicePDF to work with ApiInvoice format
      // await generateInvoicePDF(invoiceData);
      addToast({
        message: 'PDF export not yet implemented for new format',
        type: 'info'
      });
    } catch (error) {
      console.error('Error exporting PDF:', error);
      addToast({
        message: 'Failed to export PDF',
        type: 'error'
      });
    }
  }

  async function handleSendEmail() {
    if (!invoiceData || !emailAddress.trim()) {
      addToast({
        message: 'Please enter a valid email address',
        type: 'error'
      });
      return;
    }

    isSendingEmail = true;

    try {
      // Generate PDF as attachment
      const pdfBlob = await generateInvoicePDFAsBlob(invoiceData);
      const pdfBase64 = await blobToBase64(pdfBlob);
      
      const pdfAttachment: EmailAttachment = {
        filename: `${invoiceData.invoiceNumber}.pdf`,
        content: pdfBase64,
        contentType: 'application/pdf'
      };

      // Send the email
      const invoiceTotal = invoiceData.invoiceLines.reduce((sum, line) => sum + line.total, 0);
      const response = await sendInvoiceEmail(
        invoiceData.id || '',
        emailAddress,
        'Customer', // ApiInvoice doesn't have customerName
        invoiceData.invoiceNumber?.toString() || 'Draft',
        invoiceTotal,
        pdfAttachment
      );

      if (response.success) {
        addToast({
          message: 'Invoice email sent successfully',
          type: 'success'
        });
        
        // Refresh email history
        emailHistory = await getEmailHistoryForInvoice(invoiceData.id || '');
        
        // Close modal
        showEmailModal = false;
      } else {
        throw new Error(response.error || 'Failed to send email');
      }
    } catch (error) {
      console.error('Error sending email:', error);
      addToast({
        message: error instanceof Error ? error.message : 'Failed to send email',
        type: 'error'
      });
    } finally {
      isSendingEmail = false;
    }
  }

  function openEmailModal() {
    // ApiInvoice doesn't have customer info, so we'll just open the modal
    showEmailModal = true;
  }

  function closeEmailModal() {
    showEmailModal = false;
  }

  // Helper function to generate PDF as blob
  async function generateInvoicePDFAsBlob(invoice: ApiInvoice): Promise<Blob> {
    // This is a simplified version - in a real implementation, you'd generate the actual PDF blob
    // For now, we'll create a mock PDF content
    const pdfContent = `Mock PDF content for invoice ${invoice.invoiceNumber || 'Draft'}`;
    return new Blob([pdfContent], { type: 'application/pdf' });
  }

  // Helper function to convert blob to base64
  function blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // Remove the data URL prefix to get just the base64 content
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  function getEmailStatusColor(status: string): string {
    switch (status) {
      case 'sent': return '#10b981';
      case 'failed': return '#ef4444';
      case 'pending': return '#f59e0b';
      default: return '#6b7280';
    }
  }

</script>

<div class="container">
  <PageHeader title="Invoice Details">
    <svelte:fragment slot="actions">
      {#if invoiceData}
        <Button variant="secondary" on:click={() => goto(`/invoices/${invoiceData?.id}/edit`)}>
          Edit Invoice
        </Button>
        <Button variant="secondary" on:click={handleExportPDF}>
          Export PDF
        </Button>
        <Button variant="primary" on:click={openEmailModal}>
          Send Email
        </Button>
      {/if}
    </svelte:fragment>
  </PageHeader>

  <main>
    {#if isLoading}
      <LoadingSpinner message="Loading invoice details..." />
    {:else if error}
      <div class="error-message">
        <p>Error: {error}</p>
      </div>
    {:else if invoiceData}
      <div class="invoice-details">
        <div class="invoice-header">
          <h2>Invoice Number: {invoiceData.invoiceNumber || 'Draft'}</h2>
          <div class="invoice-status">
            <span class="status-badge" style="background-color: {getStatusDisplay(invoiceData.status).color}20; color: {getStatusDisplay(invoiceData.status).color};">
              {getStatusDisplay(invoiceData.status).name}
            </span>
          </div>
        </div>

        <div class="invoice-info-grid">
          <div class="info-section">
            <h3>Customer Information</h3>
            <p><strong>Name:</strong> Customer</p>
            <p><strong>Email:</strong> N/A</p>
          </div>

          <div class="info-section">
            <h3>Invoice Information</h3>
            <p><strong>Issue Date:</strong> {invoiceData.issueDate ? new Date(invoiceData.issueDate).toLocaleDateString() : 'N/A'}</p>
            <p><strong>Due Date:</strong> {invoiceData.dueDate ? new Date(invoiceData.dueDate).toLocaleDateString() : 'N/A'}</p>
            <p><strong>Total Amount:</strong> {formatCurrency(invoiceData.invoiceLines.reduce((sum, line) => sum + line.total, 0))}</p>
          </div>
        </div>

        {#if invoiceData.notes || invoiceData.paymentTerms}
          <div class="notes-section">
            {#if invoiceData.notes}
              <div>
                <h3>Notes</h3>
                <p>{invoiceData.notes}</p>
              </div>
            {/if}
            {#if invoiceData.paymentTerms}
              <div>
                <h3>Payment Terms</h3>
                <p>{invoiceData.paymentTerms}</p>
              </div>
            {/if}
          </div>
        {/if}

        <h3>Line Items</h3>
        {#if invoiceData.invoiceLines && invoiceData.invoiceLines.length > 0}
          <div class="table-container">
            <table class="line-items-table">
              <thead>
                <tr>
                  <th>Description</th>
                  <th>Quantity</th>
                  <th>Unit Price</th>
                  <th>Tax Rate</th>
                  <th>Line Total</th>
                </tr>
              </thead>
              <tbody>
                {#each invoiceData.invoiceLines as item}
                  <tr>
                    <td>{item.description}</td>
                    <td>{item.quantity}</td>
                    <td>{formatCurrency(item.unitPrice)}</td>
                    <td>{(item.taxRate * 100).toFixed(1)}%</td>
                    <td>{formatCurrency(item.total)}</td>
                  </tr>
                {/each}
              </tbody>
            </table>
          </div>
        {:else}
          <p>No line items found.</p>
        {/if}

        <!-- Email History Section -->
        {#if emailHistory.length > 0}
          <div class="email-history-section">
            <h3>Email History</h3>
            <div class="email-history-list">
              {#each emailHistory as email}
                <div class="email-history-item">
                  <div class="email-info">
                    <div class="email-header">
                      <span class="email-to">{email.to}</span>
                      <span class="email-status" style="color: {getEmailStatusColor(email.status)};">
                        {email.status}
                      </span>
                    </div>
                    <div class="email-subject">{email.subject}</div>
                    <div class="email-date">{new Date(email.sentAt).toLocaleString()}</div>
                    {#if email.error}
                      <div class="email-error">{email.error}</div>
                    {/if}
                  </div>
                </div>
              {/each}
            </div>
          </div>
        {/if}
      </div>
    {:else}
      <p>Invoice not found.</p>
    {/if}
  </main>
</div>

<!-- Email Modal -->
{#if showEmailModal}
  <div 
    class="modal-overlay" 
    on:click={closeEmailModal}
    on:keydown={(e) => e.key === 'Escape' && closeEmailModal()}
    role="dialog"
    aria-modal="true"
    aria-labelledby="modal-title"
    tabindex="-1"
  >
    <div 
      class="modal-content" 
      on:click|stopPropagation
      on:keydown={(e) => e.key === 'Escape' && closeEmailModal()}
      role="document"
    >
      <div class="modal-header">
        <h2 id="modal-title">Send Invoice Email</h2>
        <button class="close-button" on:click={closeEmailModal}>&times;</button>
      </div>
      
      <div class="modal-body">
        <div class="form-group">
          <label for="email-address">Email Address</label>
          <input
            id="email-address"
            type="email"
            bind:value={emailAddress}
            placeholder="<EMAIL>"
            required
          />
        </div>
        
        <div class="form-group">
          <label for="email-subject">Subject</label>
          <input
            id="email-subject"
            type="text"
            bind:value={emailSubject}
            placeholder="Invoice subject"
            required
          />
        </div>
        
        <div class="form-group">
          <label for="email-message">Additional Message (Optional)</label>
          <textarea
            id="email-message"
            bind:value={emailMessage}
            placeholder="Add a personal message to include with the invoice..."
            rows="4"
          ></textarea>
        </div>
        
        <div class="email-preview">
          <h4>Email Preview</h4>
          <p>The invoice will be sent as a PDF attachment with a professional email template.</p>
          {#if invoiceData}
            <div class="preview-details">
              <p><strong>Invoice:</strong> {invoiceData.invoiceNumber}</p>
              <p><strong>Amount:</strong> {formatCurrency(invoiceData.invoiceLines.reduce((sum, line) => sum + line.total, 0))}</p>
              <p><strong>Customer:</strong> Customer</p>
            </div>
          {/if}
        </div>
      </div>
      
      <div class="modal-footer">
        <Button variant="secondary" on:click={closeEmailModal} disabled={isSendingEmail}>
          Cancel
        </Button>
        <Button 
          variant="primary" 
          on:click={handleSendEmail} 
          disabled={isSendingEmail || !emailAddress.trim()}
        >
          {#if isSendingEmail}
            Sending...
          {:else}
            Send Email
          {/if}
        </Button>
      </div>
    </div>
  </div>
{/if}

<style lang="less">
  main {
    padding: 1rem 2rem;
  }

  .error-message {
    text-align: center;
    padding: 2rem;
    color: var(--danger);
  }

  .invoice-details {
    background: white;
    border-radius: var(--br);
    padding: 2rem;
    box-shadow: var(--shadow);
  }

  .invoice-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border);

    h2 {
      margin: 0;
      color: var(--black);
    }

    .status-badge {
      padding: 0.5rem 1rem;
      border-radius: var(--br);
      font-weight: 600;
      font-size: 0.875rem;
    }
  }

  .invoice-info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .info-section {
    h3 {
      margin: 0 0 1rem 0;
      color: var(--black);
      font-size: 1.125rem;
    }

    p {
      margin: 0.5rem 0;
      color: var(--grey);
    }

    .address {
      margin-left: 1rem;
      color: var(--grey);
      line-height: 1.5;
    }
  }

  .notes-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--bg-light);
    border-radius: var(--br);

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }

    h3 {
      margin: 0 0 0.5rem 0;
      color: var(--black);
    }

    p {
      margin: 0;
      color: var(--grey);
      line-height: 1.5;
    }
  }

  .table-container {
    overflow-x: auto;
    margin-bottom: 2rem;
  }

  .line-items-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: var(--br);
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    th, td {
      padding: 1rem;
      text-align: left;
      border-bottom: 1px solid var(--border);
    }

    th {
      background: var(--bg-light);
      font-weight: 600;
      color: var(--black);
    }

    td {
      color: var(--grey);
    }

    tr:last-child td {
      border-bottom: none;
    }
  }

  .email-history-section {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border);

    h3 {
      margin: 0 0 1rem 0;
      color: var(--black);
    }
  }

  .email-history-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .email-history-item {
    background: var(--bg-light);
    border-radius: var(--br);
    padding: 1rem;
    border-left: 4px solid var(--primary);

    .email-info {
      .email-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;

        .email-to {
          font-weight: 600;
          color: var(--black);
        }

        .email-status {
          font-size: 0.875rem;
          font-weight: 600;
          text-transform: uppercase;
        }
      }

      .email-subject {
        color: var(--grey);
        margin-bottom: 0.25rem;
      }

      .email-date {
        font-size: 0.875rem;
        color: var(--grey-light);
      }

      .email-error {
        margin-top: 0.5rem;
        padding: 0.5rem;
        background: var(--danger-light);
        color: var(--danger);
        border-radius: 4px;
        font-size: 0.875rem;
      }
    }
  }

  // Modal styles
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 1rem;
  }

  .modal-content {
    background: white;
    border-radius: var(--br);
    box-shadow: var(--shadow-lg);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border);

    h2 {
      margin: 0;
      color: var(--black);
    }

    .close-button {
      background: none;
      border: none;
      font-size: 1.5rem;
      cursor: pointer;
      color: var(--grey);
      padding: 0;
      width: 2rem;
      height: 2rem;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;

      &:hover {
        background: var(--bg-light);
        color: var(--black);
      }
    }
  }

  .modal-body {
    padding: 1.5rem;

    .form-group {
      margin-bottom: 1.5rem;

      label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: var(--black);
      }

      input, textarea {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid var(--border);
        border-radius: var(--br);
        font-size: 1rem;
        transition: border-color 0.2s;

        &:focus {
          outline: none;
          border-color: var(--primary);
          box-shadow: 0 0 0 3px var(--primary-light);
        }
      }

      textarea {
        resize: vertical;
        min-height: 100px;
      }
    }

    .email-preview {
      background: var(--bg-light);
      padding: 1rem;
      border-radius: var(--br);
      margin-top: 1rem;

      h4 {
        margin: 0 0 0.5rem 0;
        color: var(--black);
      }

      p {
        margin: 0 0 1rem 0;
        color: var(--grey);
        font-size: 0.875rem;
      }

      .preview-details {
        background: white;
        padding: 1rem;
        border-radius: 4px;
        border: 1px solid var(--border);

        p {
          margin: 0.25rem 0;
          font-size: 0.875rem;
        }
      }
    }
  }

  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding: 1.5rem;
    border-top: 1px solid var(--border);
    background: var(--bg-light);
  }
</style>