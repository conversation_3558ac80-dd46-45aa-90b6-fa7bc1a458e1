<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import PageHeader from '$lib/components/PageHeader.svelte';
  import Button from '$lib/components/Button.svelte';
  import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
  import { addToast } from '$lib/stores/toastStore';
  import { contactStore, customers } from '$lib/stores/customerStore';
  import { 
    createQuote, 
    getQuoteTemplates, 
    getQuoteStatuses,
    generateAIQuote,
    calculateQuoteTotals,
    type Quote,
    type QuoteSection,
    type QuoteLineItem,
    type QuoteTemplate,
    type QuoteStatus,
    type AIQuoteRequest
  } from '$lib/api/quotes';
  import type { Contact } from '$lib/api/contacts';
  import CustomerSelect from '$lib/components/CustomerSelect.svelte';

  // Form data interface
  interface QuoteFormData {
    customerId: string;
    issueDate: string;
    expiryDate: string;
    status: QuoteStatus;
    templateId: string;
    sections: QuoteSection[];
    lineItems: QuoteLineItem[];
    subtotal: number;
    taxAmount: number;
    discountAmount: number;
    totalAmount: number;
    notes: string;
    terms: string;
  }

  // State variables
  let isLoading = false;
  let isSaving = false;
  let isGeneratingAI = false;
  let quoteTemplates: QuoteTemplate[] = [];
  let quoteStatuses: QuoteStatus[] = [];
  let isLoadingData = true;
  let showAIModal = false;

  // Initialize form data
  let formData: QuoteFormData = {
    customerId: '',
    issueDate: new Date().toISOString().split('T')[0],
    expiryDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    status: { id: '1', name: 'Draft', color: '#6B7280' },
    templateId: '',
    sections: [
      createEmptySection('cover', 'Project Overview', 1),
      createEmptySection('observational', 'Observations', 2),
      createEmptySection('recommendations', 'Recommendations', 3)
    ],
    lineItems: [createEmptyLineItem()],
    subtotal: 0,
    taxAmount: 0,
    discountAmount: 0,
    totalAmount: 0,
    notes: '',
    terms: 'Quote valid for 30 days. Payment due within 30 days of acceptance.'
  };

  // AI Quote Generation form
  let aiQuoteForm: AIQuoteRequest = {
    customerInfo: {
      name: '',
      address: '',
      propertyType: 'Residential'
    },
    jobDetails: {
      jobType: '',
      description: '',
      urgency: 'Medium',
      estimatedDuration: ''
    },
    observations: [''],
    requirements: [''],
    additionalNotes: ''
  };

  // Form validation
  let errors: Record<string, string> = {};
  let formSubmitted = false;

  function generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  function createEmptySection(type: QuoteSection['type'], title: string, order: number): QuoteSection {
    return {
      id: generateId(),
      type,
      title,
      content: '',
      order,
      isVisible: true
    };
  }

  function createEmptyLineItem(): QuoteLineItem {
    return {
      id: generateId(),
      description: '',
      quantity: 1,
      unitPrice: 0,
      taxRate: 10,
      taxAmount: 0,
      lineTotal: 0,
      additionalInfo: ''
    };
  }

  function calculateTotals() {
    const updatedLineItems = formData.lineItems.map(item => {
      const lineTotal = item.quantity * item.unitPrice;
      const taxAmount = lineTotal * (item.taxRate / 100);
      return {
        ...item,
        lineTotal,
        taxAmount
      };
    });

    const totals = calculateQuoteTotals(updatedLineItems, formData.discountAmount);

    formData = {
      ...formData,
      lineItems: updatedLineItems,
      subtotal: totals.subtotal,
      taxAmount: totals.taxAmount,
      totalAmount: totals.totalAmount
    };
  }

  function addSection() {
    const newSection = createEmptySection('text', 'New Section', formData.sections.length + 1);
    formData = {
      ...formData,
      sections: [...formData.sections, newSection]
    };
  }

  function removeSection(index: number) {
    formData = {
      ...formData,
      sections: formData.sections.filter((_, i) => i !== index)
    };
  }

  function addLineItem() {
    formData = {
      ...formData,
      lineItems: [...formData.lineItems, createEmptyLineItem()]
    };
  }

  function removeLineItem(index: number) {
    formData = {
      ...formData,
      lineItems: formData.lineItems.filter((_, i) => i !== index)
    };
    calculateTotals();
  }

  function addObservation() {
    aiQuoteForm.observations = [...aiQuoteForm.observations, ''];
  }

  function removeObservation(index: number) {
    aiQuoteForm.observations = aiQuoteForm.observations.filter((_, i) => i !== index);
  }

  function addRequirement() {
    aiQuoteForm.requirements = [...aiQuoteForm.requirements, ''];
  }

  function removeRequirement(index: number) {
    aiQuoteForm.requirements = aiQuoteForm.requirements.filter((_, i) => i !== index);
  }

  async function handleGenerateAI() {
    isGeneratingAI = true;
    try {
      // Get selected customer details
      const selectedCustomer = $customers.find(c => c.id === formData.customerId);
      if (selectedCustomer) {
        aiQuoteForm.customerInfo.name = selectedCustomer.companyName || selectedCustomer.fullName;
        const primaryAddress = selectedCustomer.addresses.find(addr => addr.isPrimary);
        if (primaryAddress) {
          aiQuoteForm.customerInfo.address = `${primaryAddress.street}, ${primaryAddress.city}, ${primaryAddress.state}`;
        }
      }

      const aiResponse = await generateAIQuote(aiQuoteForm);
      
      // Update form with AI-generated content
      formData.sections = [
        {
          id: generateId(),
          type: 'cover',
          title: 'Project Overview',
          content: aiResponse.coverSection,
          order: 1,
          isVisible: true
        },
        {
          id: generateId(),
          type: 'observational',
          title: 'Observations',
          content: aiResponse.observationalData,
          order: 2,
          isVisible: true
        },
        {
          id: generateId(),
          type: 'recommendations',
          title: 'Recommendations',
          content: aiResponse.recommendations,
          order: 3,
          isVisible: true
        }
      ];

      formData.lineItems = aiResponse.lineItems.map(item => ({
        id: generateId(),
        description: item.description,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        taxRate: 10,
        taxAmount: 0,
        lineTotal: 0,
        additionalInfo: item.notes || ''
      }));

      formData.terms = aiResponse.terms;
      
      calculateTotals();
      showAIModal = false;
      
      addToast({
        message: 'AI quote generated successfully!',
        type: 'success'
      });
    } catch (error) {
      console.error('Error generating AI quote:', error);
      addToast({
        message: 'Failed to generate AI quote',
        type: 'error'
      });
    } finally {
      isGeneratingAI = false;
    }
  }

  function validateForm(): boolean {
    errors = {};

    if (!formData.customerId) {
      errors.customerId = 'Please select a customer';
    }

    if (!formData.issueDate) {
      errors.issueDate = 'Issue date is required';
    }

    if (!formData.expiryDate) {
      errors.expiryDate = 'Expiry date is required';
    }

    // Validate line items
    formData.lineItems.forEach((item, index) => {
      if (!item.description) {
        errors[`lineItems[${index}].description`] = 'Description is required';
      }

      if (item.quantity <= 0) {
        errors[`lineItems[${index}].quantity`] = 'Quantity must be greater than 0';
      }

      if (item.unitPrice < 0) {
        errors[`lineItems[${index}].unitPrice`] = 'Unit price cannot be negative';
      }
    });

    return Object.keys(errors).length === 0;
  }

  async function handleSubmit() {
    formSubmitted = true;
    calculateTotals();

    if (!validateForm()) {
      addToast({
        message: 'Please fix the errors in the form before submitting',
        type: 'error'
      });
      return;
    }

    isSaving = true;

    try {
      const selectedCustomer = $customers.find(c => c.id === formData.customerId);
      const primaryAddress = selectedCustomer?.addresses.find(addr => addr.isPrimary);
      
      const quoteData: Omit<Quote, 'id' | 'quoteNumber' | 'createdAt' | 'updatedAt'> = {
        customerId: formData.customerId,
        customerName: selectedCustomer?.companyName || selectedCustomer?.fullName,
        customerEmail: selectedCustomer?.emails.find(e => e.isPrimary)?.email,
        customerAddress: primaryAddress ? {
          street: primaryAddress.street,
          city: primaryAddress.city,
          state: primaryAddress.state,
          zipCode: primaryAddress.zipCode,
          country: primaryAddress.country
        } : undefined,
        issueDate: formData.issueDate,
        expiryDate: formData.expiryDate,
        status: formData.status,
        templateId: formData.templateId || undefined,
        sections: formData.sections,
        lineItems: formData.lineItems,
        subtotal: formData.subtotal,
        taxAmount: formData.taxAmount,
        discountAmount: formData.discountAmount,
        totalAmount: formData.totalAmount,
        notes: formData.notes,
        terms: formData.terms
      };

      const newQuote = await createQuote(quoteData);
      
      addToast({
        message: 'Quote created successfully',
        type: 'success'
      });

      goto('/quotes');
    } catch (err) {
      console.error('Error creating quote:', err);
      addToast({
        message: err instanceof Error ? err.message : 'An unknown error occurred',
        type: 'error'
      });
    } finally {
      isSaving = false;
    }
  }

  function cancelForm() {
    goto('/quotes');
  }

  async function loadData() {
    isLoadingData = true;
    try {
      await contactStore.loadContacts();
      
      const [templatesData, statusesData] = await Promise.all([
        getQuoteTemplates(),
        getQuoteStatuses()
      ]);
      
      quoteTemplates = templatesData;
      quoteStatuses = statusesData;
      
      if (quoteStatuses.length > 0) {
        formData.status = quoteStatuses.find(s => s.name === 'Draft') || quoteStatuses[0];
      }
      
    } catch (error) {
      console.error('Error loading data:', error);
      addToast({ message: 'Failed to load data', type: 'error' });
    } finally {
      isLoadingData = false;
    }
  }

  // Watch for changes to recalculate totals
  $: if (formData.lineItems) {
    calculateTotals();
  }

  onMount(async () => {
    await loadData();
  });
</script>

<svelte:head>
  <title>Create Quote</title>
</svelte:head>

<div class="container">
  <PageHeader title="Create Quote">
    <svelte:fragment slot="actions">
      <Button variant="secondary" on:click={() => showAIModal = true}>
        AI Generate
      </Button>
    </svelte:fragment>
  </PageHeader>

  <main>
    {#if isLoadingData}
      <div class="loading-container">
        <LoadingSpinner />
        <p>Loading quote data...</p>
      </div>
    {:else}
      <form on:submit|preventDefault={handleSubmit} class="quote-form">
        <!-- Quote Header -->
        <div class="form-section">
          <h2>Quote Details</h2>
          
          <div class="form-row">
            <div class="form-group">
              <label for="status">Status</label>
              <select id="status" bind:value={formData.status}>
                {#each quoteStatuses as status}
                  <option value={status}>{status.name}</option>
                {/each}
              </select>
            </div>

            <div class="form-group">
              <label for="template">Template</label>
              <select id="template" bind:value={formData.templateId}>
                <option value="">Default Template</option>
                {#each quoteTemplates as template}
                  <option value={template.id}>{template.name}</option>
                {/each}
              </select>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="issueDate">Issue Date</label>
              <input
                type="date"
                id="issueDate"
                bind:value={formData.issueDate}
                class:error={formSubmitted && errors.issueDate}
              />
              {#if formSubmitted && errors.issueDate}
                <div class="error-message">{errors.issueDate}</div>
              {/if}
            </div>

            <div class="form-group">
              <label for="expiryDate">Expiry Date</label>
              <input
                type="date"
                id="expiryDate"
                bind:value={formData.expiryDate}
                class:error={formSubmitted && errors.expiryDate}
              />
              {#if formSubmitted && errors.expiryDate}
                <div class="error-message">{errors.expiryDate}</div>
              {/if}
            </div>
          </div>
        </div>

        <!-- Customer Information -->
        <div class="form-section">
          <h2>Customer Information</h2>

          <CustomerSelect
            bind:customerId={formData.customerId}
            bind:customerSearch={aiQuoteForm.customerInfo.name}
            hasError={formSubmitted && !!errors.customerId}
            errorMessage={errors.customerId}
          />
        </div>

        <!-- Quote Sections -->
        <div class="form-section">
          <div class="section-header">
            <h2>Quote Sections</h2>
            <Button variant="secondary" size="small" on:click={addSection}>
              Add Section
            </Button>
          </div>

          {#each formData.sections as section, index}
            <div class="section-item">
              <div class="section-header-row">
                <div class="form-group">
                  <label for="sectionTitle{index}">Section Title</label>
                  <input
                    type="text"
                    id="sectionTitle{index}"
                    bind:value={section.title}
                    placeholder="Section title"
                  />
                </div>

                <div class="form-group">
                  <label for="sectionType{index}">Type</label>
                  <select id="sectionType{index}" bind:value={section.type}>
                    <option value="cover">Cover/Preamble</option>
                    <option value="observational">Observational Data</option>
                    <option value="recommendations">Recommendations</option>
                    <option value="text">Text Section</option>
                    <option value="images">Images</option>
                  </select>
                </div>

                <div class="form-group">
                  <Button
                    variant="tertiary"
                    size="small"
                    on:click={() => removeSection(index)}
                  >
                    Remove
                  </Button>
                </div>
              </div>

              <div class="form-group">
                <label for="sectionContent{index}">Content</label>
                <textarea
                  id="sectionContent{index}"
                  bind:value={section.content}
                  rows="4"
                  placeholder="Section content..."
                ></textarea>
              </div>
            </div>
          {/each}
        </div>

        <!-- Line Items -->
        <div class="form-section">
          <h2>Line Items</h2>

          <div class="items-table">
            <div class="items-header">
              <div class="item-cell">Description</div>
              <div class="item-cell">Quantity</div>
              <div class="item-cell">Unit Price</div>
              <div class="item-cell">Tax Rate (%)</div>
              <div class="item-cell">Line Total</div>
              <div class="item-cell actions"></div>
            </div>

            {#each formData.lineItems as item, index}
              <div class="items-row">
                <div class="item-cell">
                  <input
                    type="text"
                    bind:value={item.description}
                    placeholder="Item description"
                    class:error={formSubmitted && errors[`lineItems[${index}].description`]}
                  />
                  <textarea
                    bind:value={item.additionalInfo}
                    placeholder="Additional info (optional)"
                    rows="2"
                    class="additional-info"
                  ></textarea>
                  {#if formSubmitted && errors[`lineItems[${index}].description`]}
                    <div class="error-message">{errors[`lineItems[${index}].description`]}</div>
                  {/if}
                </div>

                <div class="item-cell">
                  <input
                    type="number"
                    bind:value={item.quantity}
                    min="1"
                    step="1"
                    class:error={formSubmitted && errors[`lineItems[${index}].quantity`]}
                  />
                  {#if formSubmitted && errors[`lineItems[${index}].quantity`]}
                    <div class="error-message">{errors[`lineItems[${index}].quantity`]}</div>
                  {/if}
                </div>

                <div class="item-cell">
                  <input
                    type="number"
                    bind:value={item.unitPrice}
                    min="0"
                    step="0.01"
                    class:error={formSubmitted && errors[`lineItems[${index}].unitPrice`]}
                  />
                  {#if formSubmitted && errors[`lineItems[${index}].unitPrice`]}
                    <div class="error-message">{errors[`lineItems[${index}].unitPrice`]}</div>
                  {/if}
                </div>

                <div class="item-cell">
                  <input
                    type="number"
                    bind:value={item.taxRate}
                    min="0"
                    max="100"
                    step="0.1"
                  />
                </div>

                <div class="item-cell amount">
                  ${item.lineTotal.toFixed(2)}
                </div>

                <div class="item-cell actions">
                  {#if formData.lineItems.length > 1}
                    <Button
                      variant="tertiary"
                      size="small"
                      on:click={() => removeLineItem(index)}
                    >
                      Remove
                    </Button>
                  {/if}
                </div>
              </div>
            {/each}

            <div class="add-item-row">
              <Button variant="secondary" size="small" on:click={addLineItem}>
                Add Item
              </Button>
            </div>
          </div>

          <div class="quote-totals">
            <div class="totals-row">
              <div class="totals-label">Subtotal:</div>
              <div class="totals-value">${formData.subtotal.toFixed(2)}</div>
            </div>

            <div class="totals-row">
              <div class="totals-label">
                <label for="discountAmount">Discount:</label>
              </div>
              <div class="totals-value">
                <input
                  type="number"
                  id="discountAmount"
                  bind:value={formData.discountAmount}
                  min="0"
                  step="0.01"
                  class="discount-input"
                />
              </div>
            </div>

            <div class="totals-row">
              <div class="totals-label">Tax:</div>
              <div class="totals-value">${formData.taxAmount.toFixed(2)}</div>
            </div>

            <div class="totals-row total">
              <div class="totals-label">Total:</div>
              <div class="totals-value">${formData.totalAmount.toFixed(2)}</div>
            </div>
          </div>
        </div>

        <!-- Additional Information -->
        <div class="form-section">
          <h2>Additional Information</h2>

          <div class="form-group">
            <label for="notes">Notes</label>
            <textarea id="notes" bind:value={formData.notes} rows="3" placeholder="Notes to the customer"></textarea>
          </div>

          <div class="form-group">
            <label for="terms">Terms and Conditions</label>
            <textarea id="terms" bind:value={formData.terms} rows="3"></textarea>
          </div>
        </div>

        <div class="form-actions">
          <Button variant="tertiary" on:click={cancelForm} disabled={isSaving}>Cancel</Button>
          <Button type="submit" disabled={isSaving}>
            {#if isSaving}
              Saving...
            {:else}
              Save Quote
            {/if}
          </Button>
        </div>
      </form>
    {/if}
  </main>
</div>

<!-- AI Quote Generation Modal -->
{#if showAIModal}
  <div class="modal-overlay" on:click={() => showAIModal = false}>
    <div class="modal-content" on:click|stopPropagation>
      <div class="modal-header">
        <h2>AI Quote Generation</h2>
        <button class="close-button" on:click={() => showAIModal = false}>&times;</button>
      </div>

      <div class="modal-body">
        <div class="form-group">
          <label for="jobType">Job Type</label>
          <input
            type="text"
            id="jobType"
            bind:value={aiQuoteForm.jobDetails.jobType}
            placeholder="e.g., Plumbing, Electrical, HVAC"
          />
        </div>

        <div class="form-group">
          <label for="jobDescription">Job Description</label>
          <textarea
            id="jobDescription"
            bind:value={aiQuoteForm.jobDetails.description}
            rows="3"
            placeholder="Describe the work to be done..."
          ></textarea>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="propertyType">Property Type</label>
            <select id="propertyType" bind:value={aiQuoteForm.customerInfo.propertyType}>
              <option value="Residential">Residential</option>
              <option value="Commercial">Commercial</option>
              <option value="Industrial">Industrial</option>
            </select>
          </div>

          <div class="form-group">
            <label for="urgency">Urgency</label>
            <select id="urgency" bind:value={aiQuoteForm.jobDetails.urgency}>
              <option value="Low">Low</option>
              <option value="Medium">Medium</option>
              <option value="High">High</option>
            </select>
          </div>
        </div>

        <div class="form-group">
          <label>Observations</label>
          {#each aiQuoteForm.observations as observation, index}
            <div class="array-input">
              <input
                type="text"
                bind:value={aiQuoteForm.observations[index]}
                placeholder="Observation..."
              />
              {#if aiQuoteForm.observations.length > 1}
                <Button variant="tertiary" size="small" on:click={() => removeObservation(index)}>
                  Remove
                </Button>
              {/if}
            </div>
          {/each}
          <Button variant="secondary" size="small" on:click={addObservation}>
            Add Observation
          </Button>
        </div>

        <div class="form-group">
          <label>Requirements</label>
          {#each aiQuoteForm.requirements as requirement, index}
            <div class="array-input">
              <input
                type="text"
                bind:value={aiQuoteForm.requirements[index]}
                placeholder="Requirement..."
              />
              {#if aiQuoteForm.requirements.length > 1}
                <Button variant="tertiary" size="small" on:click={() => removeRequirement(index)}>
                  Remove
                </Button>
              {/if}
            </div>
          {/each}
          <Button variant="secondary" size="small" on:click={addRequirement}>
            Add Requirement
          </Button>
        </div>

        <div class="form-group">
          <label for="additionalNotes">Additional Notes</label>
          <textarea
            id="additionalNotes"
            bind:value={aiQuoteForm.additionalNotes}
            rows="2"
            placeholder="Any additional information..."
          ></textarea>
        </div>
      </div>

      <div class="modal-footer">
        <Button variant="tertiary" on:click={() => showAIModal = false}>Cancel</Button>
        <Button on:click={handleGenerateAI} disabled={isGeneratingAI}>
          {#if isGeneratingAI}
            Generating...
          {:else}
            Generate Quote
          {/if}
        </Button>
      </div>
    </div>
  </div>
{/if}

<style lang="less">
  .quote-form {
    max-width: 1200px;
    margin: 0 auto;
  }

  .form-section {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;

    h2 {
      margin-top: 0;
      margin-bottom: 20px;
      font-size: 18px;
      color: var(--primary);
      border-bottom: 1px solid var(--border);
      padding-bottom: 10px;
    }
  }

  .form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 10px;
    }
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;

    h2 {
      margin: 0;
    }
  }

  .section-item {
    border: 1px solid var(--border);
    border-radius: var(--br);
    padding: 1rem;
    margin-bottom: 1rem;
    background: var(--bg);

    .section-header-row {
      display: grid;
      grid-template-columns: 2fr 1fr auto;
      gap: 1rem;
      align-items: end;
      margin-bottom: 1rem;
    }
  }

  .items-table {
    border: 1px solid var(--border);
    border-radius: 4px;
    margin-bottom: 20px;
  }

  .items-header {
    display: grid;
    grid-template-columns: 3fr 1fr 1fr 1fr 1fr 80px;
    background-color: var(--bg);
    gap: 10px;
    border-bottom: 1px solid var(--border);
    font-weight: 500;
    color: var(--grey);

    .item-cell {
      padding: 10px;
      font-size: 14px;
    }
  }

  .items-row {
    display: grid;
    grid-template-columns: 3fr 1fr 1fr 1fr 1fr 80px;
    border-bottom: 1px solid var(--border);
    align-items: center;
    font-size: 14px;
    gap: 10px;

    .item-cell {
      padding: 10px;

      &.amount {
        font-weight: 500;
      }

      input, textarea {
        width: 100%;
        padding: 8px;
        border: 1px solid var(--border);
        border-radius: 4px;

        &:focus {
          outline: none;
          border-color: var(--primary);
        }

        &.error {
          border-color: var(--red);
        }
      }

      .additional-info {
        margin-top: 0.5rem;
        font-size: 0.9rem;
        resize: vertical;
      }
    }
  }

  .add-item-row {
    padding: 15px;
    text-align: left;
  }

  .quote-totals {
    margin-left: auto;
    width: 300px;
    border-top: 1px solid var(--border);
    padding-top: 15px;
  }

  .totals-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;

    &.total {
      font-weight: 700;
      font-size: 18px;
      margin-top: 10px;
      padding-top: 10px;
      border-top: 1px solid var(--border);
    }
  }

  .discount-input {
    width: 100px;
    padding: 8px;
    border: 1px solid var(--border);
    border-radius: 4px;
    text-align: right;

    &:focus {
      outline: none;
      border-color: var(--primary);
    }
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    gap: 1rem;

    p {
      color: var(--grey);
    }
  }

  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border);

    h2 {
      margin: 0;
    }

    .close-button {
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: var(--grey);

      &:hover {
        color: var(--black);
      }
    }
  }

  .modal-body {
    padding: 20px;
  }

  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px;
    border-top: 1px solid var(--border);
  }

  .array-input {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 10px;

    input {
      flex: 1;
    }
  }

  @media (max-width: 768px) {
    .section-header-row {
      grid-template-columns: 1fr;
      gap: 0.5rem;
    }

    .items-header,
    .items-row {
      grid-template-columns: 1fr;
      gap: 0.5rem;
    }

    .item-cell {
      padding: 0.5rem;
    }
  }
</style> 