{"name": "ejp-frontend", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run"}, "devDependencies": {"@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@types/node": "^22.15.21", "less": "^4.3.0", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "typescript": "^5.0.0", "vite": "^6.2.6"}, "dependencies": {"html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lottie-web": "^5.12.2", "marked": "^15.0.12", "svelte-dnd-action": "^0.9.61", "svelte-lottie": "^0.0.0"}}